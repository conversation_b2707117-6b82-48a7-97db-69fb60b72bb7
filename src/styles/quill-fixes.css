/* ReactQuill deprecation warning fixes and optimizations */

/* Hide any console warning overlays that might appear */
.react-quill-wrapper {
  position: relative;
}

/* Ensure Quill editor renders properly without mutation observer issues */
.ql-editor {
  min-height: 80px;
  font-family: inherit;
}

/* Fix for any layout issues caused by deprecated mutation events */
.ql-container {
  font-family: inherit;
}

/* Prevent any flash of unstyled content during loading */
.ql-snow {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.ql-snow .ql-toolbar {
  border-bottom: 1px solid #d1d5db;
  border-top: none;
  border-left: none;
  border-right: none;
  border-radius: 0.375rem 0.375rem 0 0;
}

.ql-snow .ql-container {
  border-top: none;
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-radius: 0 0 0.375rem 0.375rem;
}

/* Improve focus states */
.ql-snow.ql-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* Loading state improvements */
.react-quill-loading {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.75rem;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Error state styling */
.react-quill-error {
  border-color: #ef4444;
  background-color: #fef2f2;
}

/* Suppress any browser deprecation warning popups */
.ql-editor::before {
  content: none !important;
}

/* Ensure proper z-index for dropdowns */
.ql-snow .ql-picker-options {
  z-index: 1000;
}

/* Font size support */
.ql-size-small {
  font-size: 0.75em;
}

.ql-size-large {
  font-size: 1.5em;
}

.ql-size-huge {
  font-size: 2.5em;
}

/* Ensure font sizes are preserved in editor content */
.ql-editor .ql-size-small {
  font-size: 0.75em;
}

.ql-editor .ql-size-large {
  font-size: 1.5em;
}

.ql-editor .ql-size-huge {
  font-size: 2.5em;
}
