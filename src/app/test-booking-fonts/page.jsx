'use client';

import { useState, useEffect } from 'react';
import ReactQuillWrapper from '@/components/common/ReactQuillWrapper';
import HtmlContentDisplay from '@/components/HtmlContentDisplay';
import 'react-quill/dist/quill.snow.css';
import '@/styles/quill-fixes.css';

const quillModules = {
  toolbar: [
    [{ 'header': [1, 2, 3, false] }],
    [{ 'size': ['small', false, 'large', 'huge'] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'align': [] }],
    ['link'],
    ['clean']
  ],
};

const quillFormats = [
  'header', 'size', 'bold', 'italic', 'underline', 'strike',
  'list', 'bullet', 'color', 'background', 'align', 'link'
];

export default function TestBookingFonts() {
  const [bookingContent, setBookingContent] = useState('');
  const [apiData, setApiData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Fetch current booking data from API
  useEffect(() => {
    const fetchBookingData = async () => {
      try {
        const response = await fetch('/api/pages');
        const data = await response.json();
        
        if (data.success) {
          setApiData(data.data);
          setBookingContent(data.data?.booking?.details || '');
        }
      } catch (error) {
        console.error('Error fetching booking data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBookingData();
  }, []);

  // Save booking content to API
  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'booking',
          data: {
            details: bookingContent
          }
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        alert('Booking content saved successfully!');
        // Refresh the API data
        setApiData(data.data);
      } else {
        alert('Failed to save booking content');
      }
    } catch (error) {
      console.error('Error saving booking content:', error);
      alert('Error saving booking content');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 p-8 flex items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-100 p-8 overflow-y-auto">
      <div className="max-w-6xl mx-auto mb-20">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Booking Font Size End-to-End Test
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Test Instructions
          </h2>
          <ol className="list-decimal list-inside text-gray-600 space-y-2">
            <li>Edit the booking content below using different font sizes</li>
            <li>Click "Save to Database" to persist the changes</li>
            <li>Check the "BookingFormComponent Simulation" to see how it displays</li>
            <li>Compare with the "Raw API Data" to verify font size classes are preserved</li>
            <li>Visit the actual booking page at <a href="/booking" className="text-blue-600 underline">/booking</a> to see the live result</li>
          </ol>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Editor Section */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              📝 PagesForm Simulation (Booking Details Editor)
            </h3>
            <ReactQuillWrapper
              theme="snow"
              value={bookingContent}
              onChange={setBookingContent}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Enter booking information and details with different font sizes..."
              style={{ minHeight: '200px' }}
              className="border rounded-md border-gray-300 mb-4"
            />
            <button
              onClick={handleSave}
              disabled={saving}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {saving ? 'Saving...' : 'Save to Database'}
            </button>
          </div>

          {/* BookingFormComponent Simulation */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              🎯 BookingFormComponent Simulation
            </h3>
            <div className="bg-black text-white p-4 rounded-md min-h-[200px]">
              <div className="text-sm text-gray-300 mb-2">
                Simulating BookingFormComponent context (black background, white text)
              </div>
              <HtmlContentDisplay htmlString={apiData?.booking?.details || bookingContent} />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          {/* Current Editor Content */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              📄 Current Editor Content (HtmlContentDisplay)
            </h3>
            <div className="bg-gray-50 p-4 rounded border min-h-[150px]">
              <HtmlContentDisplay htmlString={bookingContent} />
            </div>
          </div>

          {/* Raw API Data */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              🔍 Raw API Data (Database Content)
            </h3>
            <div className="bg-gray-100 p-4 rounded text-sm font-mono overflow-x-auto max-h-[200px] overflow-y-auto">
              <pre>{JSON.stringify(apiData?.booking, null, 2)}</pre>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">
            🧪 Font Size Test Examples
          </h3>
          <div className="space-y-4">
            <button
              onClick={() => setBookingContent('<p>This is <span class="ql-size-small">small text</span>, normal text, <span class="ql-size-large">large text</span>, and <span class="ql-size-huge">huge text</span> for booking details.</p>')}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 mr-2"
            >
              Load Font Size Test Content
            </button>
            <button
              onClick={() => setBookingContent('<p><strong>Booking Information:</strong></p><ul><li><span class="ql-size-large">Check-in: 3:00 PM</span></li><li><span class="ql-size-small">Check-out: 11:00 AM</span></li><li>Standard rate applies</li><li><span class="ql-size-huge">Contact: +267 76 123 456</span></li></ul>')}
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 mr-2"
            >
              Load Realistic Booking Content
            </button>
            <button
              onClick={() => setBookingContent('')}
              className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
            >
              Clear Content
            </button>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-6">
          <h3 className="text-lg font-medium text-yellow-800 mb-2">
            🔧 Debugging Information
          </h3>
          <div className="text-sm text-yellow-700 space-y-1">
            <p><strong>Current Content Length:</strong> {bookingContent.length} characters</p>
            <p><strong>Contains ql-size-small:</strong> {bookingContent.includes('ql-size-small') ? '✅ Yes' : '❌ No'}</p>
            <p><strong>Contains ql-size-large:</strong> {bookingContent.includes('ql-size-large') ? '✅ Yes' : '❌ No'}</p>
            <p><strong>Contains ql-size-huge:</strong> {bookingContent.includes('ql-size-huge') ? '✅ Yes' : '❌ No'}</p>
            <p><strong>API Data Available:</strong> {apiData ? '✅ Yes' : '❌ No'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
