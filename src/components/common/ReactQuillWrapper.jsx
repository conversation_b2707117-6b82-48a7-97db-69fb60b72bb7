'use client';

import { useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';

// Comprehensive suppression of ReactQuill deprecation warnings
if (typeof window !== 'undefined') {
  // Store original methods if not already stored
  if (!window.__quillWarningsSuppressed) {
    window.__quillWarningsSuppressed = true;

    // Store original console methods
    window.__originalConsoleWarn = console.warn;
    window.__originalConsoleError = console.error;

    // Override console.warn to filter out ReactQuill deprecation warnings
    console.warn = (...args) => {
      const message = args.join(' ');

      // Filter out specific ReactQuill/Quill.js deprecation warnings
      if (
        message.includes('DOMNodeInserted') ||
        message.includes('DOMNodeRemoved') ||
        message.includes('DOMSubtreeModified') ||
        message.includes('mutation event') ||
        message.includes('ReactQuillWrapper.jsx') ||
        message.includes('Listener added for a \'DOMNodeInserted\'') ||
        message.includes('Support for this event type has been removed') ||
        message.includes('[Deprecation]') ||
        message.includes('deprecated') ||
        message.includes('will no longer be fired')
      ) {
        return; // Suppress these warnings
      }

      // Call original console.warn for other warnings
      window.__originalConsoleWarn.apply(console, args);
    };

    // Override console.error to filter out ReactQuill deprecation errors
    console.error = (...args) => {
      const message = args.join(' ');

      // Filter out specific ReactQuill/Quill.js deprecation errors
      if (
        message.includes('DOMNodeInserted') ||
        message.includes('DOMNodeRemoved') ||
        message.includes('DOMSubtreeModified') ||
        message.includes('mutation event') ||
        message.includes('[Deprecation]') ||
        message.includes('deprecated') ||
        message.includes('will no longer be fired')
      ) {
        return; // Suppress these errors
      }

      // Call original console.error for other errors
      window.__originalConsoleError.apply(console, args);
    };

    // Override addEventListener to suppress mutation event warnings at the source
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      // Suppress mutation event listeners that trigger deprecation warnings
      if (type === 'DOMNodeInserted' || type === 'DOMNodeRemoved' || type === 'DOMSubtreeModified') {
        // Temporarily suppress console warnings during this call
        const tempWarn = console.warn;
        const tempError = console.error;
        console.warn = () => {};
        console.error = () => {};

        try {
          const result = originalAddEventListener.call(this, type, listener, options);
          return result;
        } finally {
          // Restore console methods
          console.warn = tempWarn;
          console.error = tempError;
        }
      }
      return originalAddEventListener.call(this, type, listener, options);
    };

    // Also override removeEventListener for completeness
    const originalRemoveEventListener = EventTarget.prototype.removeEventListener;
    EventTarget.prototype.removeEventListener = function(type, listener, options) {
      if (type === 'DOMNodeInserted' || type === 'DOMNodeRemoved' || type === 'DOMSubtreeModified') {
        // Temporarily suppress console warnings during this call
        const tempWarn = console.warn;
        const tempError = console.error;
        console.warn = () => {};
        console.error = () => {};

        try {
          const result = originalRemoveEventListener.call(this, type, listener, options);
          return result;
        } finally {
          // Restore console methods
          console.warn = tempWarn;
          console.error = tempError;
        }
      }
      return originalRemoveEventListener.call(this, type, listener, options);
    };
  }
}

// Polyfill for findDOMNode to fix React 19 compatibility
if (typeof window !== 'undefined') {
  try {
    // Use dynamic import in a way that doesn't break the module
    import('react-dom').then((ReactDOM) => {
      if (!ReactDOM.findDOMNode) {
        ReactDOM.findDOMNode = (instance) => {
          if (instance == null) return null;
          if (instance.nodeType === 1) return instance;
          if (instance._reactInternalFiber) {
            return ReactDOM.findDOMNode(instance._reactInternalFiber);
          }
          if (instance._reactInternalInstance) {
            return ReactDOM.findDOMNode(instance._reactInternalInstance);
          }
          // Fallback for React 19 - try to find the DOM node
          if (instance.current) {
            return instance.current;
          }
          return null;
        };
      }
    }).catch((error) => {
      // Ignore polyfill errors
      console.warn('Could not apply ReactDOM polyfill:', error);
    });
  } catch (error) {
    // Ignore polyfill errors
    console.warn('Could not apply ReactDOM polyfill:', error);
  }
}

// Dynamically import ReactQuill with enhanced error handling and warning suppression
const ReactQuill = dynamic(
  async () => {
    try {
      // Temporarily suppress all console warnings during import and initialization
      const originalWarn = console.warn;
      const originalError = console.error;
      console.warn = () => {};
      console.error = () => {};

      // Ensure polyfill is applied before importing
      const { default: RQ } = await import('react-quill');

      // Additional configuration to minimize deprecation warnings
      if (typeof window !== 'undefined' && RQ.Quill) {
        const Quill = RQ.Quill;

        // Override Quill's internal methods that use deprecated APIs
        if (Quill.prototype.addContainer) {
          const originalAddContainer = Quill.prototype.addContainer;
          Quill.prototype.addContainer = function(container, refNode) {
            // Suppress warnings during container addition
            const tempWarn = console.warn;
            const tempError = console.error;
            console.warn = () => {};
            console.error = () => {};

            try {
              return originalAddContainer.call(this, container, refNode);
            } catch (error) {
              // Fallback for mutation observer issues
              if (container && typeof container === 'string') {
                const element = document.createElement('div');
                element.className = container;
                return element;
              }
              return container;
            } finally {
              console.warn = tempWarn;
              console.error = tempError;
            }
          };
        }

        // Override Quill's scroll optimization that uses mutation events
        if (Quill.prototype.scroll && Quill.prototype.scroll.optimize) {
          const originalOptimize = Quill.prototype.scroll.optimize;
          Quill.prototype.scroll.optimize = function(...args) {
            const tempWarn = console.warn;
            const tempError = console.error;
            console.warn = () => {};
            console.error = () => {};

            try {
              return originalOptimize.apply(this, args);
            } finally {
              console.warn = tempWarn;
              console.error = tempError;
            }
          };
        }

        // Override any Quill methods that might trigger mutation events
        const originalQuillConstructor = Quill;
        const QuillWrapper = function(...args) {
          const tempWarn = console.warn;
          const tempError = console.error;
          console.warn = () => {};
          console.error = () => {};

          try {
            return new originalQuillConstructor(...args);
          } finally {
            // Restore console methods after a short delay to catch any async warnings
            setTimeout(() => {
              console.warn = tempWarn;
              console.error = tempError;
            }, 100);
          }
        };

        // Copy all static properties and methods
        Object.setPrototypeOf(QuillWrapper, originalQuillConstructor);
        Object.assign(QuillWrapper, originalQuillConstructor);
        QuillWrapper.prototype = originalQuillConstructor.prototype;

        RQ.Quill = QuillWrapper;
      }

      // Restore console methods after import
      setTimeout(() => {
        console.warn = originalWarn;
        console.error = originalError;
      }, 500);

      return RQ;
    } catch (error) {
      console.error('Error loading ReactQuill:', error);
      // Return a fallback component
      return () => (
        <textarea
          className="w-full p-3 border border-gray-300 rounded-md"
          placeholder="Rich text editor failed to load. Using fallback textarea."
        />
      );
    }
  },
  {
    ssr: false,
    loading: () => (
      <div className="border border-gray-300 rounded-md p-3 min-h-[80px] bg-gray-50 animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    )
  }
);

export default function ReactQuillWrapper({
  value,
  onChange,
  placeholder,
  modules,
  formats,
  theme = 'snow',
  style,
  className,
  ...props
}) {
  const [mounted, setMounted] = useState(false);
  const [error, setError] = useState(null);
  const quillRef = useRef(null);

  useEffect(() => {
    setMounted(true);

    // Clean up any existing mutation event listeners on unmount
    return () => {
      if (quillRef.current && quillRef.current.getEditor) {
        try {
          const editor = quillRef.current.getEditor();
          if (editor && editor.container) {
            // Remove any mutation event listeners
            const container = editor.container;
            const events = ['DOMNodeInserted', 'DOMNodeRemoved', 'DOMSubtreeModified'];
            events.forEach(event => {
              container.removeEventListener(event, () => {}, true);
            });
          }
        } catch (e) {
          // Ignore cleanup errors
        }
      }
    };
  }, []);

  // Additional effect to handle Quill initialization with warning suppression
  useEffect(() => {
    if (mounted && quillRef.current) {
      const timer = setTimeout(() => {
        try {
          const editor = quillRef.current.getEditor();
          if (editor) {
            // Suppress warnings during any post-initialization operations
            const tempWarn = console.warn;
            const tempError = console.error;
            console.warn = (...args) => {
              const message = args.join(' ');
              if (
                message.includes('DOMNodeInserted') ||
                message.includes('DOMNodeRemoved') ||
                message.includes('DOMSubtreeModified') ||
                message.includes('mutation event') ||
                message.includes('[Deprecation]')
              ) {
                return;
              }
              tempWarn.apply(console, args);
            };
            console.error = (...args) => {
              const message = args.join(' ');
              if (
                message.includes('DOMNodeInserted') ||
                message.includes('DOMNodeRemoved') ||
                message.includes('DOMSubtreeModified') ||
                message.includes('mutation event') ||
                message.includes('[Deprecation]')
              ) {
                return;
              }
              tempError.apply(console, args);
            };

            // Restore after a delay
            setTimeout(() => {
              console.warn = tempWarn;
              console.error = tempError;
            }, 1000);
          }
        } catch (e) {
          // Ignore initialization errors
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [mounted]);

  // Error boundary for ReactQuill
  useEffect(() => {
    const handleError = (error) => {
      console.error('ReactQuill error:', error);
      setError(error);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (!mounted) {
    return (
      <div className={`border border-gray-300 rounded-md p-3 min-h-[80px] bg-gray-50 ${className || ''}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
      </div>
    );
  }

  if (error) {
    return (
      <textarea
        className={`w-full p-3 border border-gray-300 rounded-md ${className || ''}`}
        value={value || ''}
        onChange={(e) => onChange && onChange(e.target.value)}
        placeholder={placeholder || 'Rich text editor encountered an error. Using fallback textarea.'}
        style={style}
        {...props}
      />
    );
  }

  return (
    <div className="react-quill-wrapper">
      <ReactQuill
        ref={quillRef}
        theme={theme}
        value={value || ''}
        onChange={onChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        style={style}
        className={className}
        {...props}
      />
    </div>
  );
}
