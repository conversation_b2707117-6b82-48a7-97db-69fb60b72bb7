'use client';

import { useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';

// Comprehensive suppression of ReactQuill deprecation warnings
if (typeof window !== 'undefined') {
  // Store original methods if not already stored
  if (!window.__quillWarningsSuppressed) {
    window.__quillWarningsSuppressed = true;

    // Store original console methods
    window.__originalConsoleWarn = console.warn;
    window.__originalConsoleError = console.error;

    // Override console.warn to filter out ReactQuill deprecation warnings
    console.warn = (...args) => {
      const message = args.join(' ');

      // Filter out specific ReactQuill/Quill.js deprecation warnings
      if (
        message.includes('DOMNodeInserted') ||
        message.includes('DOMNodeRemoved') ||
        message.includes('DOMSubtreeModified') ||
        message.includes('mutation event') ||
        message.includes('ReactQuillWrapper.jsx') ||
        message.includes('Listener added for a \'DOMNodeInserted\'') ||
        message.includes('Support for this event type has been removed') ||
        message.includes('[Deprecation]') ||
        message.includes('deprecated') ||
        message.includes('will no longer be fired')
      ) {
        return; // Suppress these warnings
      }

      // Call original console.warn for other warnings
      window.__originalConsoleWarn.apply(console, args);
    };

    // Override console.error to filter out ReactQuill deprecation errors
    console.error = (...args) => {
      const message = args.join(' ');

      // Filter out specific ReactQuill/Quill.js deprecation errors
      if (
        message.includes('DOMNodeInserted') ||
        message.includes('DOMNodeRemoved') ||
        message.includes('DOMSubtreeModified') ||
        message.includes('mutation event') ||
        message.includes('[Deprecation]') ||
        message.includes('deprecated') ||
        message.includes('will no longer be fired')
      ) {
        return; // Suppress these errors
      }

      // Call original console.error for other errors
      window.__originalConsoleError.apply(console, args);
    };

    // Override addEventListener to suppress mutation event warnings at the source
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      // Suppress mutation event listeners that trigger deprecation warnings
      if (type === 'DOMNodeInserted' || type === 'DOMNodeRemoved' || type === 'DOMSubtreeModified') {
        // Temporarily suppress console warnings during this call
        const tempWarn = console.warn;
        const tempError = console.error;
        console.warn = () => {};
        console.error = () => {};

        try {
          const result = originalAddEventListener.call(this, type, listener, options);
          return result;
        } finally {
          // Restore console methods
          console.warn = tempWarn;
          console.error = tempError;
        }
      }
      return originalAddEventListener.call(this, type, listener, options);
    };

    // Also override removeEventListener for completeness
    const originalRemoveEventListener = EventTarget.prototype.removeEventListener;
    EventTarget.prototype.removeEventListener = function(type, listener, options) {
      if (type === 'DOMNodeInserted' || type === 'DOMNodeRemoved' || type === 'DOMSubtreeModified') {
        // Temporarily suppress console warnings during this call
        const tempWarn = console.warn;
        const tempError = console.error;
        console.warn = () => {};
        console.error = () => {};

        try {
          const result = originalRemoveEventListener.call(this, type, listener, options);
          return result;
        } finally {
          // Restore console methods
          console.warn = tempWarn;
          console.error = tempError;
        }
      }
      return originalRemoveEventListener.call(this, type, listener, options);
    };
  }
}



// Dynamically import ReactQuill with error handling
const ReactQuill = dynamic(
  async () => {
    try {
      // Import ReactQuill
      const { default: RQ } = await import('react-quill');
      return RQ;
    } catch (error) {
      console.error('Error loading ReactQuill:', error);
      // Return a fallback component
      return ({ value, onChange, placeholder, className, style }) => (
        <textarea
          className={`w-full p-3 border border-gray-300 rounded-md ${className || ''}`}
          value={value || ''}
          onChange={(e) => onChange && onChange(e.target.value)}
          placeholder={placeholder || 'Rich text editor failed to load. Using fallback textarea.'}
          style={style}
          rows={6}
        />
      );
    }
  },
  {
    ssr: false,
    loading: () => (
      <div className="border border-gray-300 rounded-md p-3 min-h-[80px] bg-gray-50 animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    )
  }
);


export default function ReactQuillWrapper({
  value,
  onChange,
  placeholder,
  modules,
  formats,
  theme = 'snow',
  style,
  className,
  ...props
}) {
  const [mounted, setMounted] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Error boundary for ReactQuill
  useEffect(() => {
    const handleError = (event) => {
      if (event.error && event.error.message && event.error.message.includes('findDOMNode')) {
        console.warn('ReactQuill findDOMNode error caught and suppressed');
        setError(event.error);
        event.preventDefault();
      }
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (!mounted) {
    return (
      <div className={`border border-gray-300 rounded-md p-3 min-h-[80px] bg-gray-50 ${className || ''}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
      </div>
    );
  }

  if (error) {
    return (
      <textarea
        className={`w-full p-3 border border-gray-300 rounded-md ${className || ''}`}
        value={value || ''}
        onChange={(e) => onChange && onChange(e.target.value)}
        placeholder={placeholder || 'Rich text editor encountered an error. Using fallback textarea.'}
        style={style}
        rows={6}
        {...props}
      />
    );
  }

  return (
    <div className="react-quill-wrapper">
      <ReactQuill
        theme={theme}
        value={value || ''}
        onChange={onChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        style={style}
        className={className}
        {...props}
      />
    </div>
  );
}
